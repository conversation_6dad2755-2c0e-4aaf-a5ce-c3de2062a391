import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:line_icons/line_icons.dart';
import '../providers/theme_provider.dart';
import 'enhanced_liquid_glass_navbar.dart';
import '../utils/app_themes.dart';

/// Demo screen to showcase different liquid glass navigation bar styles
/// This allows testing and comparison of different liquid glass effects
class LiquidGlassDemoScreen extends ConsumerStatefulWidget {
  const LiquidGlassDemoScreen({super.key});

  @override
  ConsumerState<LiquidGlassDemoScreen> createState() =>
      _LiquidGlassDemoScreenState();
}

class _LiquidGlassDemoScreenState extends ConsumerState<LiquidGlassDemoScreen> {
  int _selectedIndex = 0;
  int _demoMode =
      0; // 0: Standard, 1: Enhanced with animations, 2: Performance mode

  final List<String> _demoModes = [
    'Standard Liquid Glass',
    'Enhanced with Animations',
    'Performance Optimized',
  ];

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ref.watch(themeNotifierProvider.notifier).isDarkMode;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Liquid Glass Navigation Demo'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              ref.read(themeNotifierProvider.notifier).toggleTheme();
            },
          ),
        ],
      ),
      extendBody: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [
                    const Color(0xFF1A1A1A),
                    const Color(0xFF2D2D2D),
                    const Color(0xFF1A1A1A),
                  ]
                : [
                    const Color(0xFFF5F5F5),
                    const Color(0xFFE8E8E8),
                    const Color(0xFFF5F5F5),
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Demo mode selector
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  elevation: 8,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Navigation Style',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 12),
                        ...List.generate(_demoModes.length, (index) {
                          return RadioListTile<int>(
                            title: Text(_demoModes[index]),
                            value: index,
                            groupValue: _demoMode,
                            onChanged: (value) {
                              setState(() {
                                _demoMode = value!;
                              });
                            },
                            activeColor: AppThemes.primaryColor,
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),

              // Demo content area
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        LineIcons.gem,
                        size: 80,
                        color: AppThemes.primaryColor,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Liquid Glass Navigation',
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppThemes.primaryColor,
                            ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Experience modern glassmorphism design',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color:
                                  isDarkMode ? Colors.white70 : Colors.black54,
                            ),
                      ),
                      const SizedBox(height: 40),

                      // Feature highlights
                      _buildFeatureCard(
                        'Premium Glass Effects',
                        'Advanced blur and transparency',
                        LineIcons.eye,
                        isDarkMode,
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureCard(
                        'Smooth Animations',
                        'Fluid transitions and interactions',
                        LineIcons.magic,
                        isDarkMode,
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureCard(
                        'Performance Optimized',
                        'Efficient rendering for all devices',
                        LineIcons.rocket,
                        isDarkMode,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildDemoNavigationBar(isDarkMode),
    );
  }

  Widget _buildFeatureCard(
      String title, String subtitle, IconData icon, bool isDarkMode) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color:
            isDarkMode ? Colors.white.withAlpha(10) : Colors.black.withAlpha(5),
        border: Border.all(
          color: isDarkMode
              ? Colors.white.withAlpha(20)
              : Colors.black.withAlpha(10),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppThemes.primaryColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDemoNavigationBar(bool isDarkMode) {
    switch (_demoMode) {
      case 1: // Enhanced with animations
        return EnhancedLiquidGlassNavBar(
          selectedIndex: _selectedIndex,
          isDarkMode: isDarkMode,
          enablePulseEffect: true,
          enableParallaxEffect: true,
          tabs: const [
            GButton(icon: LineIcons.home, text: 'Home'),
            GButton(icon: LineIcons.palette, text: 'Design'),
            GButton(icon: LineIcons.cog, text: 'Settings'),
            GButton(icon: LineIcons.infoCircle, text: 'About'),
          ],
          onTabChange: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        );

      case 2: // Performance optimized
        return EnhancedLiquidGlassNavBar(
          selectedIndex: _selectedIndex,
          isDarkMode: isDarkMode,
          enablePulseEffect: false,
          enableParallaxEffect: false,
          tabs: const [
            GButton(icon: LineIcons.home, text: 'Home'),
            GButton(icon: LineIcons.palette, text: 'Design'),
            GButton(icon: LineIcons.cog, text: 'Settings'),
            GButton(icon: LineIcons.infoCircle, text: 'About'),
          ],
          onTabChange: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        );

      default: // Standard liquid glass
        return EnhancedLiquidGlassNavBar(
          selectedIndex: _selectedIndex,
          isDarkMode: isDarkMode,
          enablePulseEffect: false,
          enableParallaxEffect: true,
          tabs: const [
            GButton(icon: LineIcons.home, text: 'Home'),
            GButton(icon: LineIcons.palette, text: 'Design'),
            GButton(icon: LineIcons.cog, text: 'Settings'),
            GButton(icon: LineIcons.infoCircle, text: 'About'),
          ],
          onTabChange: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        );
    }
  }
}
