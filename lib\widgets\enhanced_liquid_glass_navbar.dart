import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:line_icons/line_icons.dart';
import '../utils/app_themes.dart';

/// Enhanced Liquid Glass Navigation Bar with advanced visual effects
/// This component provides a premium liquid glass navigation experience
/// with modern design patterns and smooth animations
class EnhancedLiquidGlassNavBar extends StatefulWidget {
  final int selectedIndex;
  final Function(int) onTabChange;
  final bool isDarkMode;
  final List<GButton> tabs;
  final double? height;
  final EdgeInsets? margin;
  final bool enablePulseEffect;
  final bool enableParallaxEffect;

  const EnhancedLiquidGlassNavBar({
    super.key,
    required this.selectedIndex,
    required this.onTabChange,
    required this.isDarkMode,
    required this.tabs,
    this.height = 85.0,
    this.margin = const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 12.0),
    this.enablePulseEffect = true,
    this.enableParallaxEffect = true,
  });

  @override
  State<EnhancedLiquidGlassNavBar> createState() => _EnhancedLiquidGlassNavBarState();
}

class _EnhancedLiquidGlassNavBarState extends State<EnhancedLiquidGlassNavBar>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _parallaxController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _parallaxAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize pulse animation for breathing effect
    _pulseController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Initialize parallax animation for depth effect
    _parallaxController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _parallaxAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _parallaxController,
      curve: Curves.easeOutCubic,
    ));

    if (widget.enablePulseEffect) {
      _pulseController.repeat(reverse: true);
    }
    
    if (widget.enableParallaxEffect) {
      _parallaxController.forward();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _parallaxController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _parallaxAnimation]),
      builder: (context, child) {
        return Container(
          height: widget.height,
          margin: widget.margin,
          child: Stack(
            children: [
              // Background blur layer with animated depth
              _buildBackgroundBlur(),
              
              // Main liquid glass navigation bar
              _buildMainNavigation(),
              
              // Top highlight with pulse effect
              _buildTopHighlight(),
              
              // Side glow effects for premium appearance
              if (widget.enablePulseEffect) _buildSideGlowEffects(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBackgroundBlur() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(32),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: 20.0 + (_pulseAnimation.value * 5.0),
          sigmaY: 20.0 + (_pulseAnimation.value * 5.0),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: widget.isDarkMode
                  ? [
                      Colors.white.withAlpha((15 * _parallaxAnimation.value).round()),
                      Colors.white.withAlpha((5 * _parallaxAnimation.value).round()),
                      Colors.black.withAlpha((20 * _parallaxAnimation.value).round()),
                    ]
                  : [
                      Colors.white.withAlpha((40 * _parallaxAnimation.value).round()),
                      Colors.white.withAlpha((20 * _parallaxAnimation.value).round()),
                      Colors.black.withAlpha((10 * _parallaxAnimation.value).round()),
                    ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainNavigation() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(32),
      child: LiquidGlass(
        settings: LiquidGlassSettings(
          thickness: 24 + (_pulseAnimation.value * 4),
          glassColor: widget.isDarkMode
              ? Color.lerp(
                  const Color(0x20FFFFFF),
                  const Color(0x30FFFFFF),
                  _pulseAnimation.value,
                )!
              : Color.lerp(
                  const Color(0x18000000),
                  const Color(0x25000000),
                  _pulseAnimation.value,
                )!,
          lightIntensity: 2.5 + (_pulseAnimation.value * 0.5),
          ambientStrength: 0.7 + (_pulseAnimation.value * 0.2),
          blend: 65 + (_pulseAnimation.value * 10).round(),
          lightAngle: 1.2 + (_pulseAnimation.value * 0.3),
        ),
        shape: LiquidRoundedSuperellipse(
          borderRadius: Radius.circular(32),
        ),
        glassContainsChild: true,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: widget.isDarkMode
                  ? [
                      Colors.white.withAlpha((25 * _parallaxAnimation.value).round()),
                      Colors.transparent,
                      Colors.black.withAlpha((15 * _parallaxAnimation.value).round()),
                    ]
                  : [
                      Colors.white.withAlpha((50 * _parallaxAnimation.value).round()),
                      Colors.transparent,
                      Colors.black.withAlpha((8 * _parallaxAnimation.value).round()),
                    ],
              stops: const [0.0, 0.6, 1.0],
            ),
            border: Border.all(
              color: widget.isDarkMode
                  ? Colors.white.withAlpha((20 * _parallaxAnimation.value).round())
                  : Colors.black.withAlpha((15 * _parallaxAnimation.value).round()),
              width: 0.5,
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
          child: GNav(
            rippleColor: widget.isDarkMode
                ? Colors.white.withAlpha(40)
                : Colors.black.withAlpha(40),
            hoverColor: widget.isDarkMode
                ? Colors.white.withAlpha(25)
                : Colors.black.withAlpha(25),
            gap: 10,
            activeColor: Colors.white,
            iconSize: 26,
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
            duration: const Duration(milliseconds: 300),
            tabBackgroundColor: AppThemes.primaryColor.withAlpha(240),
            color: widget.isDarkMode
                ? Colors.white.withAlpha(180)
                : Colors.black.withAlpha(200),
            textStyle: const TextStyle(
              fontWeight: FontWeight.w700,
              color: Colors.white,
              fontSize: 15,
              letterSpacing: 0.5,
            ),
            curve: Curves.easeInOutCubic,
            tabs: widget.tabs,
            selectedIndex: widget.selectedIndex,
            onTabChange: (index) {
              HapticFeedback.lightImpact();
              widget.onTabChange(index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTopHighlight() {
    return Positioned(
      top: 1,
      left: 1,
      right: 1,
      child: Container(
        height: 2 + (_pulseAnimation.value * 1),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(32),
            topRight: Radius.circular(32),
          ),
          gradient: LinearGradient(
            colors: [
              Colors.white.withAlpha(
                widget.isDarkMode 
                    ? (40 * _pulseAnimation.value).round()
                    : (60 * _pulseAnimation.value).round()
              ),
              Colors.transparent,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSideGlowEffects() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: AppThemes.primaryColor.withAlpha(
                (30 * _pulseAnimation.value).round()
              ),
              blurRadius: 20 * _pulseAnimation.value,
              spreadRadius: 2 * _pulseAnimation.value,
            ),
          ],
        ),
      ),
    );
  }
}
